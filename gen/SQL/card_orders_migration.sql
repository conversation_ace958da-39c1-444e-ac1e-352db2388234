-- 卡牌2.0订单系统数据库迁移脚本
-- 创建card_orders表，扩展messages和conversations表以支持订单消息功能

-- =====================================================
-- 1. 创建卡牌交易订单表
-- =====================================================
CREATE TABLE `card_orders` (
    `id` VARCHAR(64) NOT NULL COMMENT '订单ID（同时作为订单号显示给用户）',
    `conversation_group_id` VARCHAR(64) NOT NULL COMMENT '会话组ID（关联conversations表的conversation_group_id）',
    `buyer_id` VARCHAR(64) NOT NULL COMMENT '买家ID',
    `seller_id` VARCHAR(64) NOT NULL COMMENT '卖家ID',
    `card_items` JSON NOT NULL COMMENT '卡片信息，类型:[]CardItem',
    `first_card_image_url` VARCHAR(500) NOT NULL COMMENT '第一张卡牌图片URL（用于订单展示）',
    `card_groups_count` INT NOT NULL DEFAULT 0 COMMENT '卡片组数',
    `total_quantity` INT NOT NULL DEFAULT 0 COMMENT '卡片总数量',
    `total_amount` BIGINT NOT NULL COMMENT '订单总金额(分)',
    `pay_amount` BIGINT NOT NULL COMMENT '支付金额(分)',
    `payment_method` TINYINT DEFAULT NULL COMMENT '支付方式（如：1=卡牌钱包）',
    `transaction_no` VARCHAR(64) COMMENT '支付流水单号',
    `status` TINYINT NOT NULL DEFAULT 0 COMMENT '订单状态：0=待支付 10=待发货 20=已发货 30=已完成 40=已取消',
    `cancel_type` TINYINT DEFAULT NULL COMMENT '取消类型：1=买家取消 2=卖家取消 3=超时取消',
    `shipping_address` JSON COMMENT '收货地址信息，类型:Address',
    `payment_at` DATETIME(3) DEFAULT NULL COMMENT '支付时间',
    `delivered_at` DATETIME(3) DEFAULT NULL COMMENT '发货时间',
    `received_at` DATETIME(3) DEFAULT NULL COMMENT '收货时间',
    `expired_at` DATETIME(3) NOT NULL COMMENT '订单过期时间',
    `finished_at` DATETIME(3) DEFAULT NULL COMMENT '订单完成时间',
    `cancel_at` DATETIME(3) DEFAULT NULL COMMENT '订单取消时间',
    `buyer_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '买家是否删除：0=否 1=是',
    `seller_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '卖家是否删除：0=否 1=是',
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
∑    
    -- 核心业务索引设计（基于实际查询模式优化）
    
    -- 买家订单列表查询优化：WHERE buyer_id = ? AND buyer_deleted = 0 ORDER BY created_at DESC
    INDEX `idx_buyer_orders_list` (`buyer_id`, `buyer_deleted`, `created_at` DESC),
    
    -- 卖家订单列表查询优化：WHERE seller_id = ? AND seller_deleted = 0 ORDER BY created_at DESC
    INDEX `idx_seller_orders_list` (`seller_id`, `seller_deleted`, `created_at` DESC),
    
    -- 会话组关联查询优化：WHERE conversation_group_id = ?
    INDEX `idx_conversation_group_id` (`conversation_group_id`),
    
    -- 管理端复合查询优化：WHERE status = ? AND created_at >= ? ORDER BY created_at DESC
    INDEX `idx_admin_status_time` (`status`, `created_at` DESC),
    
    
    -- 过期订单清理查询优化：WHERE status = 0 AND expired_at <= NOW()
    INDEX `idx_expired_orders` (`status`, `expired_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='卡牌交易订单表';

-- =====================================================
-- 2. 扩展消息表结构，新增订单相关字段
-- =====================================================
-- 在原有messages表基础上新增订单相关字段
ALTER TABLE `messages`
ADD COLUMN `order_id` VARCHAR(64) DEFAULT NULL COMMENT '关联的订单ID（订单消息类型时使用）' AFTER `post_snapshot`,
ADD COLUMN `order_snapshot` JSON DEFAULT NULL COMMENT '订单快照信息，类型:OrderSnapshot' AFTER `order_id`;

-- 更新消息类型注释，新增订单消息类型
ALTER TABLE `messages`
MODIFY COLUMN `message_type` TINYINT NOT NULL COMMENT '消息类型：1=文本 2=图片 3=帖子快照 4=订单消息';

-- =====================================================
-- 3. 扩展会话表，添加会话组ID字段
-- =====================================================
-- 添加会话组ID字段，用于关联同一对话的双向记录
ALTER TABLE `conversations`
ADD COLUMN `conversation_group_id` VARCHAR(64) NOT NULL COMMENT '会话组ID（同一对话的买家和卖家记录共享此ID）' AFTER `id`;

-- 为会话组ID添加索引以提高查询性能
ALTER TABLE `conversations` ADD INDEX `idx_conversation_group_id` (`conversation_group_id`);

-- 未读消息统计查询优化：WHERE participant_id = ? AND is_deleted = 0 AND unread_count > 0
ALTER TABLE `conversations` ADD INDEX `idx_unread_messages` (`participant_id`, `is_deleted`, `unread_count`);

-- =====================================================
-- 索引优化：删除冗余索引
-- =====================================================
-- 删除冗余索引
ALTER TABLE `conversations` DROP INDEX IF EXISTS `idx_type_participant`;
ALTER TABLE `conversations` DROP INDEX IF EXISTS `idx_type_other_participant`;

-- 更新会话表最后消息类型注释，新增订单消息类型
ALTER TABLE `conversations`
MODIFY COLUMN `last_message_type` TINYINT DEFAULT NULL COMMENT '最后消息类型：1=文本 2=图片 3=帖子快照 4=订单消息';


-- =====================================================
-- 4. 数据迁移脚本：为现有会话记录生成conversation_group_id
-- =====================================================
-- 注意：此脚本需要根据实际业务逻辑调整
-- 为现有的conversations记录生成conversation_group_id
-- 假设：participant_id较小的用户作为group_id的基准

-- 数据迁移脚本：为现有会话生成conversation_group_id
-- 规则：participant_type=1的记录，将ID转为数字+2；participant_type=2的记录，将ID转为数字+1

-- 方案1：基于participant_type和ID数值计算生成group_id（推荐用于现有数据迁移）
UPDATE conversations 
SET conversation_group_id = CASE 
    WHEN participant_type = 1 THEN CAST(CAST(id AS UNSIGNED) + 2 AS CHAR)  -- 用户类型：数字ID+2
    WHEN participant_type = 2 THEN CAST(CAST(id AS UNSIGNED) + 1 AS CHAR)  -- 商家类型：数字ID+1
    ELSE CAST(CAST(id AS UNSIGNED) + 0 AS CHAR)  -- 其他类型：数字ID+0（兜底）
END
WHERE conversation_group_id IS NULL OR conversation_group_id = '';

-- 方案2：使用参与者ID组合生成group_id（备用方案）
-- UPDATE conversations 
-- SET conversation_group_id = CONCAT(
--     LEAST(participant_id, other_participant_id), 
--     '_', 
--     GREATEST(participant_id, other_participant_id)
-- )
-- WHERE conversation_group_id IS NULL OR conversation_group_id = '';

-- 注意：
-- 1. 方案1确保了基于participant_type的规则生成group_id
-- 2. 双向会话记录需要通过业务逻辑确保使用相同的group_id
-- 3. 新创建的会话将使用雪花ID生成group_id

-- =====================================================
-- 验证脚本（执行前请取消注释）
-- =====================================================
-- 验证card_orders表创建结果
DESCRIBE card_orders;

-- 验证messages表字段添加结果
-- DESCRIBE messages;

-- 验证conversations表注释更新结果
-- SHOW FULL COLUMNS FROM conversations WHERE Field = 'last_message_type';

-- 验证索引创建结果
-- SHOW INDEX FROM card_orders;
-- SHOW INDEX FROM messages WHERE Key_name = 'idx_order_id';
