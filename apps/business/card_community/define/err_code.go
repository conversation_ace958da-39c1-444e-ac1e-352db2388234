package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	// 帖子相关错误 (500001-500099)
	CC500001Err = response.NewError(500001, "帖子不存在")
	CC500002Err = response.NewError(500002, "帖子状态无效")
	CC500003Err = response.NewError(500003, "无权限操作帖子")
	CC500004Err = response.NewError(500004, "帖子描述不能为空")
	CC500005Err = response.NewError(500005, "收购价格必须大于0")
	CC500006Err = response.NewError(500006, "收购价格最多只能输入一位小数")
	CC500007Err = response.NewError(500007, "媒体文件格式错误")
	CC500008Err = response.NewError(500008, "帖子不能编辑")
	CC500009Err = response.NewError(500009, "帖子已下架")
	CC500010Err = response.NewError(500010, "帖子不能进行状态修改")

	// 会话相关错误 (500101-500199)
	CC500101Err = response.NewError(500101, "会话不存在")
	CC500102Err = response.NewError(500102, "会话ID不能为空")
	CC500103Err = response.NewError(500103, "对方ID不能为空")
	CC500104Err = response.NewError(500104, "对方用户不存在")
	CC500105Err = response.NewError(500105, "获取用户信息失败")
	CC500106Err = response.NewError(500106, "不能与自己创建会话")

	// 消息相关错误 (500201-500299)
	CC500201Err = response.NewError(500201, "消息内容为空")
	CC500202Err = response.NewError(500202, "消息内容过长")
	CC500203Err = response.NewError(500203, "图片上传失败")
	CC500204Err = response.NewError(500204, "消息重复，请勿重复发送")
	CC500205Err = response.NewError(500205, "接收者ID不能为空")
	CC500206Err = response.NewError(500206, "消息类型无效")
	CC500207Err = response.NewError(500207, "帖子快照发送失败")
	CC500208Err = response.NewError(500208, "你已向 Ta 发送 5 条消息，请耐心等待回复～")

	// 智能回复相关错误 (500301-500399)
	CC500301Err = response.NewError(500301, "模板不存在")
	CC500302Err = response.NewError(500302, "模板内容为空")

	// 商家申请相关错误 (500601-500699)
	CC500601Err = response.NewError(500601, "您已提交过申请，无需重复申请")
	CC500602Err = response.NewError(500602, "申请记录不存在")

	// 订单相关错误 (500402-500499)
	CC500402Err = response.NewError(500402, "订单不存在")
	CC500403Err = response.NewError(500403, "订单已删除")
	CC500404Err = response.NewError(500404, "无权限操作订单")
	CC500405Err = response.NewError(500405, "订单已过期")
	CC500406Err = response.NewError(500406, "暂时无法创建订单")
	CC500407Err = response.NewError(500407, "订单不允许进行操作")

	// 支付相关错误 (500701-500799)
	CC500701Err = response.NewError(500701, "实名认证未通过")
	CC500702Err = response.NewError(500702, "请先添加收货地址")
	CC500703Err = response.NewError(500703, "支付失败")
	CC500704Err = response.NewError(500704, "余额不足")
	CC500705Err = response.NewError(500705, "支付密码错误")

	// 推送通知相关错误 (500801-500899)
	CC500801Err = response.NewError(500801, "推送服务不可用")
	CC500802Err = response.NewError(500802, "推送消息格式无效")
	CC500803Err = response.NewError(500803, "推送目标用户不存在")
)
