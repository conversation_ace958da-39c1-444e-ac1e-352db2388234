# card_orders表索引分析报告

## 表结构概览

**表名**: `card_orders` (卡牌交易订单表)

**主要字段**:
- `id` (varchar(64)) - 订单ID，主键
- `buyer_id` (varchar(64)) - 买家ID  
- `seller_id` (varchar(64)) - 卖家ID
- `status` (tinyint) - 订单状态：0=待支付 10=待发货 20=已发货 30=已完成 40=已取消
- `buyer_deleted` (tinyint(1)) - 买家是否删除：0=否 1=是
- `seller_deleted` (tinyint(1)) - 卖家是否删除：0=否 1=是  
- `conversation_group_id` (varchar(64)) - 会话组ID
- `transaction_no` (varchar(64)) - 支付流水单号
- `created_at` (datetime(3)) - 创建时间

## 核心查询场景分析

### 1. 用户端订单列表查询 (高频)

**买家视角**:
```go
// 代码位置: order_web.go:238-240
queryBuilder = search.NewQueryBuilder().
    Eq(cardOrderSchema.BuyerID, userID).
    Eq(cardOrderSchema.BuyerDeleted, false).
    OrderByDesc(cardOrderSchema.CreatedAt)
```
**对应SQL**: `WHERE buyer_id = ? AND buyer_deleted = 0 ORDER BY created_at DESC`
**推荐索引**: `idx_buyer_orders_list (buyer_id, buyer_deleted, created_at DESC)`

**卖家视角**:
```go  
// 代码位置: order_web.go:244-246
queryBuilder = search.NewQueryBuilder().
    Eq(cardOrderSchema.SellerID, userID).
    Eq(cardOrderSchema.SellerDeleted, false).
    OrderByDesc(cardOrderSchema.CreatedAt)
```
**对应SQL**: `WHERE seller_id = ? AND seller_deleted = 0 ORDER BY created_at DESC`
**推荐索引**: `idx_seller_orders_list (seller_id, seller_deleted, created_at DESC)`

### 2. 状态筛选查询 (中频)

```go
// 代码位置: order_web.go:251
if req.Status != nil {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.Status, req.Status.Int32())
}
```
**对应SQL**: `WHERE status = ?` (结合上述买家/卖家条件)
**推荐索引**: `idx_status (status)` + 复合索引已覆盖

### 3. 订单统计查询 (中频)

**买家统计**:
```go
// 代码位置: order_web.go:775-783  
buyerQueryWrapper := search.NewQueryBuilder().
    Eq(cardOrderSchema.BuyerID, userID).
    Eq(cardOrderSchema.BuyerDeleted, false).
    In(cardOrderSchema.Status, []int32{0,10,20})
```
**对应SQL**: `WHERE buyer_id = ? AND buyer_deleted = 0 AND status IN (0,10,20)`
**推荐索引**: `idx_buyer_stats (buyer_id, buyer_deleted, status)`

**卖家统计**:
```go
// 代码位置: order_web.go:794-802
sellerQueryWrapper := search.NewQueryBuilder().
    Eq(cardOrderSchema.SellerID, userID).
    Eq(cardOrderSchema.SellerDeleted, false).
    In(cardOrderSchema.Status, []int32{10})
```
**对应SQL**: `WHERE seller_id = ? AND seller_deleted = 0 AND status IN (10)`  
**推荐索引**: `idx_seller_stats (seller_id, seller_deleted, status)`

### 4. 管理端查询 (低频但复杂)

**基础管理查询**:
```go
// 代码位置: order_admin.go:31-41
if req.Status != nil {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.Status, req.Status.Int32())
}
if !req.StartTime.IsZero() {
    queryBuilder = queryBuilder.Gte(cardOrderSchema.CreatedAt, req.StartTime)
}
queryBuilder = queryBuilder.OrderByDesc(cardOrderSchema.CreatedAt)
```
**对应SQL**: `WHERE status = ? AND created_at >= ? ORDER BY created_at DESC`
**推荐索引**: `idx_admin_status_time (status, created_at DESC)`

**买家维度查询**:
```go
// 代码位置: order_admin.go:54-56
if req.BuyerID != "" {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.BuyerID, req.BuyerID)
}
```
**推荐索引**: `idx_admin_buyer_status_time (buyer_id, status, created_at DESC)`

### 5. 订单详情查询 (高频)

```go
// 代码位置: order_web.go:135
queryWrapper := search.NewQueryBuilder().
    Eq(cardOrderSchema.ID, req.OrderID).
    Build()
```
**对应SQL**: `WHERE id = ?`
**索引**: 主键索引已覆盖，无需额外索引

## 索引设计总结

| 索引名 | 字段组合 | 查询场景 | 频率 | 性能影响 |
|--------|----------|----------|------|----------|
| idx_buyer_orders_list | buyer_id, buyer_deleted, created_at DESC | 买家订单列表 | 高频 | 关键 |
| idx_seller_orders_list | seller_id, seller_deleted, created_at DESC | 卖家订单列表 | 高频 | 关键 |  
| idx_status | status | 状态筛选 | 中频 | 重要 |
| idx_buyer_stats | buyer_id, buyer_deleted, status | 买家统计 | 中频 | 重要 |
| idx_seller_stats | seller_id, seller_deleted, status | 卖家统计 | 中频 | 重要 |
| idx_admin_status_time | status, created_at DESC | 管理端查询 | 低频 | 一般 |
| idx_conversation_group_id | conversation_group_id | 会话关联 | 低频 | 一般 |
| idx_transaction_no | transaction_no | 支付查询 | 低频 | 一般 |

## 优化建议

1. **优先创建高频索引**: 买家和卖家订单列表索引是最关键的
2. **复合索引顺序**: 等值查询字段在前，排序字段在后
3. **索引覆盖**: 复合索引可以覆盖前缀字段的单独查询
4. **监控索引使用**: 定期检查索引命中率，移除未使用的索引
5. **分区考虑**: 如果数据量很大，可考虑按时间分区
