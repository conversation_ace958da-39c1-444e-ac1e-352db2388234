-- =====================================================
-- card_orders表索引与代码对应关系
-- =====================================================

-- 1. 买家订单列表查询索引
CREATE INDEX idx_buyer_orders_list ON card_orders (buyer_id, buyer_deleted, created_at DESC);
/*
对应代码位置: apps/business/card_community/service/order_web.go:238-240
查询逻辑:
```go
queryBuilder = search.NewQueryBuilder().
    Eq(cardOrderSchema.BuyerID, userID).
    Eq(cardOrderSchema.BuyerDeleted, false).
    OrderByDesc(cardOrderSchema.CreatedAt)
```
生成SQL: WHERE buyer_id = ? AND buyer_deleted = 0 ORDER BY created_at DESC
*/

-- 2. 卖家订单列表查询索引  
CREATE INDEX idx_seller_orders_list ON card_orders (seller_id, seller_deleted, created_at DESC);
/*
对应代码位置: apps/business/card_community/service/order_web.go:244-246
查询逻辑:
```go
queryBuilder = search.NewQueryBuilder().
    Eq(cardOrderSchema.SellerID, userID).
    Eq(cardOrderSchema.SellerDeleted, false).
    OrderByDesc(cardOrderSchema.CreatedAt)
```
生成SQL: WHERE seller_id = ? AND seller_deleted = 0 ORDER BY created_at DESC
*/

-- 3. 订单状态筛选索引
CREATE INDEX idx_status ON card_orders (status);
/*
对应代码位置: apps/business/card_community/service/order_web.go:250-252
查询逻辑:
```go
if req.Status != nil {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.Status, req.Status.Int32())
}
```
生成SQL: WHERE status = ? (结合买家/卖家条件)
*/

-- 4. 买家订单统计查询索引
CREATE INDEX idx_buyer_stats ON card_orders (buyer_id, buyer_deleted, status);
/*
对应代码位置: apps/business/card_community/service/order_web.go:775-783
查询逻辑:
```go
buyerQueryWrapper := search.NewQueryBuilder().
    Eq(cardOrderSchema.BuyerID, userID).
    Eq(cardOrderSchema.BuyerDeleted, false).
    In(cardOrderSchema.Status, []int32{
        enums.OrderStatusUnPaid.Int32(),
        enums.OrderStatusUnDelivered.Int32(),
        enums.OrderStatusUnReceive.Int32(),
    })
```
生成SQL: WHERE buyer_id = ? AND buyer_deleted = 0 AND status IN (0,10,20)
*/

-- 5. 卖家订单统计查询索引
CREATE INDEX idx_seller_stats ON card_orders (seller_id, seller_deleted, status);
/*
对应代码位置: apps/business/card_community/service/order_web.go:794-802
查询逻辑:
```go
sellerQueryWrapper := search.NewQueryBuilder().
    Eq(cardOrderSchema.SellerID, userID).
    Eq(cardOrderSchema.SellerDeleted, false).
    In(cardOrderSchema.Status, []int32{
        enums.OrderStatusUnDelivered.Int32(),
    })
```
生成SQL: WHERE seller_id = ? AND seller_deleted = 0 AND status IN (10)
*/

-- 6. 管理端状态时间查询索引
CREATE INDEX idx_admin_status_time ON card_orders (status, created_at DESC);
/*
对应代码位置: apps/business/card_community/service/order_admin.go:31-41
查询逻辑:
```go
if req.Status != nil {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.Status, req.Status.Int32())
}
if !req.StartTime.IsZero() {
    queryBuilder = queryBuilder.Gte(cardOrderSchema.CreatedAt, req.StartTime)
}
if !req.EndTime.IsZero() {
    queryBuilder = queryBuilder.Lte(cardOrderSchema.CreatedAt, req.EndTime)
}
queryBuilder = queryBuilder.OrderByDesc(cardOrderSchema.CreatedAt)
```
生成SQL: WHERE status = ? AND created_at >= ? AND created_at <= ? ORDER BY created_at DESC
*/

-- 7. 管理端买家查询索引
CREATE INDEX idx_admin_buyer_status_time ON card_orders (buyer_id, status, created_at DESC);
/*
对应代码位置: apps/business/card_community/service/order_admin.go:54-56
查询逻辑:
```go
if req.BuyerID != "" {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.BuyerID, req.BuyerID)
}
```
结合状态和时间筛选，按创建时间倒序
生成SQL: WHERE buyer_id = ? AND status = ? ORDER BY created_at DESC
*/

-- 8. 管理端卖家查询索引
CREATE INDEX idx_admin_seller_status_time ON card_orders (seller_id, status, created_at DESC);
/*
对应代码位置: apps/business/card_community/service/order_admin.go:59-61
查询逻辑:
```go
if req.SellerID != "" {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.SellerID, req.SellerID)
}
```
结合状态和时间筛选，按创建时间倒序
生成SQL: WHERE seller_id = ? AND status = ? ORDER BY created_at DESC
*/

-- 9. 会话组关联查询索引
CREATE INDEX idx_conversation_group_id ON card_orders (conversation_group_id);
/*
对应代码位置: apps/business/card_community/service/order_admin.go:49-51
查询逻辑:
```go
if req.ConversationGroupID != "" {
    queryBuilder = queryBuilder.Eq(cardOrderSchema.ConversationGroupID, req.ConversationGroupID)
}
```
生成SQL: WHERE conversation_group_id = ?
*/

-- 10. 支付流水号查询索引
CREATE INDEX idx_transaction_no ON card_orders (transaction_no);
/*
对应代码位置: 支付相关查询场景
查询逻辑: 根据支付流水号查询订单
生成SQL: WHERE transaction_no = ?
*/

-- 11. 订单详情查询 (主键索引已覆盖)
/*
对应代码位置: apps/business/card_community/service/order_web.go:134-136
查询逻辑:
```go
queryWrapper := search.NewQueryBuilder().
    Eq(cardOrderSchema.ID, req.OrderID).
    Build()
```
生成SQL: WHERE id = ?
索引: PRIMARY KEY (id) - 主键索引已自动创建
*/
