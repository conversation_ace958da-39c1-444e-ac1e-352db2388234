-- =====================================================
-- card_orders表索引设计
-- 基于实际业务查询场景优化的索引方案
-- =====================================================

-- 1. 买家订单列表查询索引
-- 查询场景: WHERE buyer_id = ? AND buyer_deleted = 0 ORDER BY created_at DESC
-- 代码位置: apps/business/card_community/service/order_web.go:238-240
CREATE INDEX idx_buyer_orders_list ON card_orders (buyer_id, buyer_deleted, created_at DESC);

-- 2. 卖家订单列表查询索引  
-- 查询场景: WHERE seller_id = ? AND seller_deleted = 0 ORDER BY created_at DESC
-- 代码位置: apps/business/card_community/service/order_web.go:244-246
CREATE INDEX idx_seller_orders_list ON card_orders (seller_id, seller_deleted, created_at DESC);

-- 3. 订单状态筛选索引
-- 查询场景: WHERE status = ?
-- 代码位置: apps/business/card_community/service/order_web.go:251
CREATE INDEX idx_status ON card_orders (status);

-- 4. 会话组关联查询索引
-- 查询场景: WHERE conversation_group_id = ?
-- 代码位置: 订单与会话关联查询
CREATE INDEX idx_conversation_group_id ON card_orders (conversation_group_id);

-- 5. 订单详情查询索引 (主键已存在，无需额外索引)
-- 查询场景: WHERE id = ?
-- 代码位置: apps/business/card_community/service/order_web.go:135

-- 6. 管理端状态时间复合查询索引
-- 查询场景: WHERE status = ? AND created_at >= ? ORDER BY created_at DESC
-- 代码位置: apps/business/card_community/service/order_admin.go:31-41
CREATE INDEX idx_admin_status_time ON card_orders (status, created_at DESC);

-- 7. 管理端买家状态时间查询索引
-- 查询场景: WHERE buyer_id = ? AND status = ? ORDER BY created_at DESC
-- 代码位置: apps/business/card_community/service/order_admin.go:54-56
CREATE INDEX idx_admin_buyer_status_time ON card_orders (buyer_id, status, created_at DESC);

-- 8. 管理端卖家状态时间查询索引
-- 查询场景: WHERE seller_id = ? AND status = ? ORDER BY created_at DESC  
-- 代码位置: apps/business/card_community/service/order_admin.go:59-61
CREATE INDEX idx_admin_seller_status_time ON card_orders (seller_id, status, created_at DESC);

-- 9. 支付流水号查询索引
-- 查询场景: WHERE transaction_no = ?
-- 代码位置: 支付相关查询
CREATE INDEX idx_transaction_no ON card_orders (transaction_no);

-- 10. 订单统计查询索引 (买家多状态查询)
-- 查询场景: WHERE buyer_id = ? AND buyer_deleted = 0 AND status IN (0,10,20)
-- 代码位置: apps/business/card_community/service/order_web.go:775-783
CREATE INDEX idx_buyer_stats ON card_orders (buyer_id, buyer_deleted, status);

-- 11. 订单统计查询索引 (卖家多状态查询)
-- 查询场景: WHERE seller_id = ? AND seller_deleted = 0 AND status IN (10)
-- 代码位置: apps/business/card_community/service/order_web.go:794-802
CREATE INDEX idx_seller_stats ON card_orders (seller_id, seller_deleted, status);

-- =====================================================
-- 索引使用说明
-- =====================================================

/*
索引设计原则:
1. 基于实际查询代码分析，确保每个索引都有对应的业务场景
2. 复合索引字段顺序: 等值查询字段在前，范围查询和排序字段在后
3. 考虑查询频率: 用户端高频查询优先，管理端查询次之
4. 避免冗余索引: 复合索引可以覆盖单字段查询需求

主要查询模式:
1. 用户订单列表 (最高频): buyer_id/seller_id + deleted + 排序
2. 状态筛选: 在订单列表基础上增加status条件  
3. 管理端查询: 支持多维度筛选和时间范围查询
4. 订单详情: 基于主键的单条查询
5. 统计查询: 多状态IN查询用于数量统计

索引覆盖查询:
- idx_buyer_orders_list 可覆盖 (buyer_id), (buyer_id, buyer_deleted) 查询
- idx_seller_orders_list 可覆盖 (seller_id), (seller_id, seller_deleted) 查询  
- idx_admin_buyer_status_time 可覆盖 (buyer_id), (buyer_id, status) 查询
- idx_admin_seller_status_time 可覆盖 (seller_id), (seller_id, status) 查询
*/
